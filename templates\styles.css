/* Import Google Font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Register Container */
.register-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    width: 350px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: fadeIn 1s ease-in-out;
}

h2 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 20px;
}

/* Input Fields */
input[type="text"] {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: none;
    border-radius: 5px;
    outline: none;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transition: 0.3s ease;
}

input[type="text"]::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

input[type="text"]:focus {
    background: rgba(255, 255, 255, 0.3);
}

/* Camera Container */
.camera-container {
    position: relative;
    margin-top: 20px;
}

video {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.3);
}

canvas {
    display: none;
}

/* Buttons */
button {
    width: 100%;
    padding: 12px;
    margin-top: 15px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}

#capture-button {
    background: #ff4b2b;
    color: white;
    margin-top: 10px;
}

#capture-button:hover {
    background: #ff3a1f;
    transform: scale(1.05);
}

button[type="submit"] {
    background: #4CAF50;
    color: white;
    margin-top: 15px;
}

button[type="submit"]:hover {
    background: #45a049;
    transform: scale(1.05);
}

/* Message Box */
#msg {
    margin-top: 15px;
    font-size: 14px;
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
