from django.shortcuts import render
import json
import base64
import os
import tempfile
from django.core.files.base import ContentFile
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
import face_recognition as fr
from .models import UserImages

@csrf_exempt
def register(request):
    if request.method == 'POST':
        try:
            print("=== REGISTER DEBUG ===")
            print("POST data keys:", list(request.POST.keys()))
            print("FILES data keys:", list(request.FILES.keys()) if hasattr(request, 'FILES') else 'No FILES')
            print("Content type:", request.content_type)

            # Validate input data
            username = request.POST.get('username', '').strip()
            face_image_data = request.POST.get('face_image', '')

            print(f"Username: '{username}'")
            print(f"Face image data length: {len(face_image_data) if face_image_data else 0}")
            print("=== END DEBUG ===")

            if not username:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Username is required'
                }, status=400)

            if len(username) < 3:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Username must be at least 3 characters long'
                }, status=400)

            if not face_image_data:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Face image is required'
                }, status=400)

            # Validate and process face image
            try:
                if ',' not in face_image_data:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Invalid image format'
                    }, status=400)

                face_image_data = face_image_data.split(",")[1]
                image_binary = base64.b64decode(face_image_data)

                # Create temporary file to validate image with face_recognition
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    temp_file.write(image_binary)
                    temp_file_path = temp_file.name

                try:
                    print(f"Temp file created: {temp_file_path}")
                    print(f"File size: {os.path.getsize(temp_file_path)} bytes")

                    # Validate that the image contains a face
                    test_image = fr.load_image_file(temp_file_path)
                    print(f"Image loaded, shape: {test_image.shape}")

                    # Try different face detection models for better accuracy
                    face_locations = fr.face_locations(test_image, model="hog")  # Try HOG first (faster)
                    print(f"Face locations found (HOG): {len(face_locations)}")

                    if not face_locations:
                        # Try CNN model if HOG fails (more accurate but slower)
                        print("Trying CNN model...")
                        face_locations = fr.face_locations(test_image, model="cnn")
                        print(f"Face locations found (CNN): {len(face_locations)}")

                    if not face_locations:
                        # Try with different number of upsamples
                        print("Trying with more upsamples...")
                        face_locations = fr.face_locations(test_image, number_of_times_to_upsample=2)
                        print(f"Face locations found (upsampled): {len(face_locations)}")

                    if face_locations:
                        # Get face encodings
                        face_encodings = fr.face_encodings(test_image, face_locations)
                        print(f"Face encodings generated: {len(face_encodings)}")

                        if not face_encodings:
                            return JsonResponse({
                                'status': 'error',
                                'message': 'Face detected but could not generate encoding. Please try again with better lighting.'
                            }, status=400)
                    else:
                        return JsonResponse({
                            'status': 'error',
                            'message': 'No face detected in the image. Please ensure your face is clearly visible, well-lit, and looking towards the camera.'
                        }, status=400)

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

                face_image = ContentFile(image_binary, name=f'{username}_.jpeg')

            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid image data or processing error'
                }, status=400)

            # Create user
            try:
                user = User.objects.create_user(username=username)
            except IntegrityError:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Username already exists. Please choose a different username.'
                }, status=400)
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Failed to create user account'
                }, status=500)

            # Save user image
            try:
                UserImages.objects.create(user=user, face_image=face_image)
            except Exception as e:
                # If image saving fails, delete the created user
                user.delete()
                return JsonResponse({
                    'status': 'error',
                    'message': 'Failed to save face image'
                }, status=500)

            return JsonResponse({
                'status': 'success',
                'message': 'User registered successfully! You can now login with your face.'
            })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': 'An unexpected error occurred. Please try again.'
            }, status=500)

    return render(request, 'register.html')

@csrf_exempt
def login(request):
    if request.method == "POST":
        try:
            print("=== LOGIN DEBUG START ===")
            print(f"Request content type: {request.content_type}")
            print(f"Request body length: {len(request.body)}")

            # Parse JSON request
            try:
                data = json.loads(request.body)
                print("JSON parsed successfully")
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid JSON data'
                }, status=400)

            # Validate input data
            username = data.get('username', '').strip()
            face_image_data = data.get('face_image', '')

            if not username:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Username is required'
                }, status=400)

            if not face_image_data:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Face image is required'
                }, status=400)

            # Check if user exists
            try:
                user = User.objects.get(username=username)
            except User.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': 'User does not exist. Please register first.'
                }, status=404)

            # Get user's stored face image
            try:
                user_images = UserImages.objects.filter(user=user).last()
                if not user_images:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'No face image found for this user. Please register again.'
                    }, status=404)
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Error retrieving user data'
                }, status=500)

            # Process uploaded face image
            try:
                print(f"Processing face image data...")
                if ',' not in face_image_data:
                    print("Error: No comma in face image data")
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Invalid image format'
                    }, status=400)

                face_image_data = face_image_data.split(",")[1]  # Remove Base64 prefix
                print(f"Base64 data length after split: {len(face_image_data)}")

                try:
                    image_binary = base64.b64decode(face_image_data)
                    print(f"Decoded image binary length: {len(image_binary)}")
                except Exception as decode_error:
                    print(f"Base64 decode error: {decode_error}")
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Failed to decode image data'
                    }, status=400)

                # Create temporary file for face recognition processing
                print("Creating temporary file...")
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                    temp_file.write(image_binary)
                    temp_file_path = temp_file.name
                    print(f"Temporary file created: {temp_file_path}")

                try:
                    # Load and process uploaded image
                    uploaded_face_image = fr.load_image_file(temp_file_path)
                    uploaded_face_encoding = fr.face_encodings(uploaded_face_image)

                    if not uploaded_face_encoding:
                        return JsonResponse({
                            'status': 'error',
                            'message': 'No face detected in the uploaded image. Please capture a clear photo of your face.'
                        }, status=400)

                    uploaded_face_encoding = uploaded_face_encoding[0]

                    # Load and process stored image
                    stored_face_image = fr.load_image_file(user_images.face_image.path)
                    stored_face_encoding = fr.face_encodings(stored_face_image)

                    if not stored_face_encoding:
                        return JsonResponse({
                            'status': 'error',
                            'message': 'Error processing stored face image. Please register again.'
                        }, status=500)

                    # Compare faces with tolerance
                    matches = fr.compare_faces([stored_face_encoding[0]], uploaded_face_encoding, tolerance=0.6)
                    face_distance = fr.face_distance([stored_face_encoding[0]], uploaded_face_encoding)[0]

                    if matches[0] and face_distance < 0.6:
                        return JsonResponse({
                            'status': 'success',
                            'message': f'Welcome back, {username}! Login successful.'
                        })
                    else:
                        return JsonResponse({
                            'status': 'error',
                            'message': 'Face not recognized. Please try again or register if you are a new user.'
                        }, status=401)

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Error processing face image'
                }, status=500)

        except Exception as e:
            print(f"=== LOGIN ERROR ===")
            print(f"Error type: {type(e).__name__}")
            print(f"Error message: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            print("=== END LOGIN ERROR ===")
            return JsonResponse({
                'status': 'error',
                'message': f'An unexpected error occurred: {str(e)}'
            }, status=500)

    return render(request, 'login.html')