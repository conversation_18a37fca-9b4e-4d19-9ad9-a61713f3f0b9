/* === Ultra Modern Professional Register Design === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: #2d3748;
    overflow-x: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="90" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
    pointer-events: none;
    z-index: 1;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

/* === Premium Glass Morphism Container === */
.register-container {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 40px;
    width: 450px;
    max-width: 90vw;
    text-align: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.register-container:hover {
    transform: translateY(-5px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* === Modern Typography === */
h2 {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 12px auto 24px;
    border-radius: 2px;
}

/* === Premium Input Fields === */
input[type="text"] {
    width: 100%;
    padding: 16px 20px;
    margin: 16px 0;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    font-size: 16px;
    color: #2d3748;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
}

input[type="text"]::placeholder {
    color: rgba(45, 55, 72, 0.6);
    font-weight: 400;
}

input[type="text"]:focus {
    border-color: rgba(102, 126, 234, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

input[type="text"]:valid {
    border-color: rgba(72, 187, 120, 0.6);
}

/* === Premium Camera Section === */
.camera-container {
    position: relative;
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

/* === Face Detection Guide === */
.face-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 250px;
    border: 3px solid rgba(102, 126, 234, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 10;
    animation: pulse 2s infinite;
}

.face-guide::before {
    content: 'Position your face here';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(102, 126, 234, 0.8);
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

@keyframes pulse {
    0%, 100% {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        border-color: rgba(102, 126, 234, 0.9);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.1);
    }
}

.face-guide.hidden {
    display: none;
}

/* === Futuristic Video Stream === */
video {
    width: 100%;
    max-width: 320px;
    height: 220px;
    object-fit: cover;
    border-radius: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

video::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

video:hover {
    transform: scale(1.02);
    border-color: rgba(255, 255, 255, 0.5);
}

canvas {
    display: none;
}

/* === Premium Capture Button === */
#capture-button {
    position: relative;
    width: 100%;
    padding: 14px 28px;
    margin-top: 16px;
    border: none;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 50px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 16px rgba(255, 107, 107, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#capture-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#capture-button:hover::before {
    left: 100%;
}

#capture-button:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 24px rgba(255, 107, 107, 0.5),
        0 4px 12px rgba(0, 0, 0, 0.15);
}

#capture-button:active {
    transform: translateY(0);
    box-shadow:
        0 2px 8px rgba(255, 107, 107, 0.4),
        0 1px 4px rgba(0, 0, 0, 0.1);
}

/* === Premium Submit Button === */
button[type="submit"] {
    position: relative;
    width: 100%;
    padding: 16px 24px;
    margin-top: 20px;
    border: none;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 16px rgba(72, 187, 120, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button[type="submit"]:hover::before {
    left: 100%;
}

button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 24px rgba(72, 187, 120, 0.5),
        0 4px 12px rgba(0, 0, 0, 0.15);
}

button[type="submit"]:active {
    transform: translateY(0);
    box-shadow:
        0 2px 8px rgba(72, 187, 120, 0.4),
        0 1px 4px rgba(0, 0, 0, 0.1);
}

/* === Premium Message Display === */
#msg {
    margin-top: 20px;
    font-size: 14px;
    font-weight: 500;
    padding: 16px 20px;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#msg.message.success {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.15), rgba(56, 161, 105, 0.1));
    border-color: rgba(72, 187, 120, 0.3);
    color: #38a169;
    box-shadow: 0 4px 16px rgba(72, 187, 120, 0.2);
}

#msg.message.error {
    background: linear-gradient(135deg, rgba(245, 101, 101, 0.15), rgba(229, 62, 62, 0.1));
    border-color: rgba(245, 101, 101, 0.3);
    color: #e53e3e;
    box-shadow: 0 4px 16px rgba(245, 101, 101, 0.2);
}

#msg.message.info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.1));
    border-color: rgba(102, 126, 234, 0.3);
    color: #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

/* === Premium Button States === */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    background: rgba(160, 174, 192, 0.6) !important;
}

button:disabled::before {
    display: none;
}

/* === Loading Animation === */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* === Advanced Responsive Design === */
@media (max-width: 768px) {
    .register-container {
        width: 95%;
        padding: 30px 20px;
        margin: 20px;
    }

    h2 {
        font-size: 24px;
    }

    video {
        max-width: 300px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .register-container {
        width: 100%;
        margin: 10px;
        padding: 25px 15px;
        border-radius: 20px;
    }

    h2 {
        font-size: 22px;
    }

    video {
        max-width: 280px;
        height: 180px;
    }

    #capture-button,
    button[type="submit"] {
        padding: 14px 20px;
        font-size: 14px;
    }

    input[type="text"] {
        padding: 14px 16px;
        font-size: 14px;
    }
}

@media (max-width: 320px) {
    .register-container {
        padding: 20px 10px;
    }

    video {
        max-width: 250px;
        height: 160px;
    }
}

/* === Dark mode support === */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    .register-container {
        background: rgba(26, 32, 44, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
    }

    input[type="text"] {
        color: #e2e8f0;
        background: rgba(26, 32, 44, 0.6);
    }

    input[type="text"]::placeholder {
        color: rgba(226, 232, 240, 0.6);
    }
}
