document.addEventListener("DOMContentLoaded", function () {
    console.log("DOM fully loaded and parsed");

    const video = document.getElementById("video");
    const canvas = document.getElementById("canvas");
    const capture_button = document.getElementById("capture-button");
    const messageDiv = document.getElementById("msg");
    const registerForm = document.getElementById("register-form");
    const submitButton = registerForm.querySelector('button[type="submit"]');
    const faceGuide = document.getElementById("face-guide");

    if (!capture_button) {
        console.error("Capture button not found!");
        showMessage("Error: Capture button not found!", "error");
        return;
    }

    let capturedImage = null;
    let isCapturing = false;

    // Initialize camera with optimal settings
    const constraints = {
        video: {
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
            facingMode: "user",
            frameRate: { ideal: 30, min: 15 }
        }
    };

    navigator.mediaDevices
        .getUserMedia(constraints)
        .then((stream) => {
            video.srcObject = stream;

            // Wait for video to be ready
            video.addEventListener('loadedmetadata', () => {
                console.log('Video ready:', {
                    width: video.videoWidth,
                    height: video.videoHeight
                });
                showMessage("Camera ready! Please position your face in the center and capture.", "info");
            });
        })
        .catch((err) => {
            console.error("Error accessing camera:", err);
            // Try with basic constraints if advanced ones fail
            navigator.mediaDevices
                .getUserMedia({ video: true })
                .then((stream) => {
                    video.srcObject = stream;
                    showMessage("Camera ready with basic settings! Please capture your face.", "info");
                })
                .catch((basicErr) => {
                    console.error("Basic camera access failed:", basicErr);
                    showMessage("Camera is not accessible. Please allow camera permissions and refresh the page.", "error");
                });
        });

    // Capture face image
    capture_button.addEventListener("click", () => {
        if (isCapturing) return;

        if (!video.srcObject) {
            showMessage("Please allow access to your camera first.", "error");
            return;
        }

        if (video.videoWidth === 0 || video.videoHeight === 0) {
            showMessage("Camera is not ready yet. Please wait a moment.", "error");
            return;
        }

        isCapturing = true;
        capture_button.disabled = true;
        capture_button.textContent = "Capturing...";

        try {
            const context = canvas.getContext("2d");

            // Set optimal canvas size for face recognition
            const optimalWidth = 640;
            const optimalHeight = 480;
            canvas.width = optimalWidth;
            canvas.height = optimalHeight;

            // Calculate scaling to maintain aspect ratio
            const videoAspect = video.videoWidth / video.videoHeight;
            const canvasAspect = optimalWidth / optimalHeight;

            let drawWidth, drawHeight, offsetX, offsetY;

            if (videoAspect > canvasAspect) {
                // Video is wider than canvas
                drawHeight = optimalHeight;
                drawWidth = drawHeight * videoAspect;
                offsetX = (optimalWidth - drawWidth) / 2;
                offsetY = 0;
            } else {
                // Video is taller than canvas
                drawWidth = optimalWidth;
                drawHeight = drawWidth / videoAspect;
                offsetX = 0;
                offsetY = (optimalHeight - drawHeight) / 2;
            }

            // Fill background with black
            context.fillStyle = '#000000';
            context.fillRect(0, 0, optimalWidth, optimalHeight);

            // Draw video frame with proper scaling
            context.drawImage(video, offsetX, offsetY, drawWidth, drawHeight);

            // Capture with high quality
            capturedImage = canvas.toDataURL("image/jpeg", 0.95);

            console.log('Image captured:', {
                originalSize: `${video.videoWidth}x${video.videoHeight}`,
                canvasSize: `${canvas.width}x${canvas.height}`,
                imageDataLength: capturedImage.length
            });

            showMessage("Face captured successfully! You can now register.", "success");
            capture_button.textContent = "Recapture Face";
        } catch (error) {
            console.error("Error capturing image:", error);
            showMessage("Failed to capture image. Please try again.", "error");
        } finally {
            isCapturing = false;
            capture_button.disabled = false;
        }
    });

    // Handle form submission
    registerForm.addEventListener("submit", async (e) => {
        e.preventDefault();

        if (!capturedImage) {
            showMessage("Please capture your face before registering.", "error");
            return;
        }

        const username = document.getElementById("userName").value.trim();
        if (!username) {
            showMessage("Please enter a username.", "error");
            return;
        }

        if (username.length < 3) {
            showMessage("Username must be at least 3 characters long.", "error");
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.textContent = "Registering...";
        showMessage("Processing registration...", "info");

        try {
            const formData = new FormData();
            formData.append('username', username);
            formData.append('face_image', capturedImage);
            formData.append('csrfmiddlewaretoken', getCSRFToken());

            console.log('Sending registration data:', {
                username: username,
                face_image_length: capturedImage ? capturedImage.length : 0,
                csrf_token: getCSRFToken()
            });

            const response = await fetch('/register/', {
                method: 'POST',
                body: formData
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                showMessage(data.message || "Registration successful!", "success");
                // Reset form after successful registration
                setTimeout(() => {
                    registerForm.reset();
                    capturedImage = null;
                    capture_button.textContent = "Capture Face";
                    showMessage("You can now login with your face!", "info");
                }, 2000);
            } else {
                showMessage(data.message || "Registration failed. Please try again.", "error");
            }
        } catch (error) {
            console.error("Registration error:", error);
            showMessage("Network error. Please check your connection and try again.", "error");
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = "Register";
        }
    });

    // Helper function to get CSRF token
    function getCSRFToken() {
        const cookieValue = document.cookie.match(/csrftoken=([^;]+)/);
        return cookieValue ? cookieValue[1] : "";
    }

    // Helper function to show messages with different styles
    function showMessage(message, type = "info") {
        messageDiv.innerHTML = message;
        messageDiv.className = `message ${type}`;

        // Remove message after 5 seconds for success/info messages
        if (type === "success" || type === "info") {
            setTimeout(() => {
                if (messageDiv.innerHTML === message) {
                    messageDiv.innerHTML = "";
                    messageDiv.className = "";
                }
            }, 5000);
        }
    }
});

