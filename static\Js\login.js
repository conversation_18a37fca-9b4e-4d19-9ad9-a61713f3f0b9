document.addEventListener("DOMContentLoaded", function () {
    const video = document.getElementById("video");
    const canvas = document.getElementById("canvas");
    const captureBtn = document.getElementById("capture-btn");
    const loginForm = document.getElementById("login-form");
    const messageDiv = document.getElementById("message");
    const submitButton = loginForm.querySelector('button[type="submit"]');

    let capturedImage = null;
    let isCapturing = false;
    let isCameraReady = false;

    // Initialize camera with optimal settings
    const constraints = {
        video: {
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
            facingMode: "user",
            frameRate: { ideal: 30, min: 15 }
        }
    };

    navigator.mediaDevices.getUserMedia(constraints)
        .then((stream) => {
            video.srcObject = stream;
            video.addEventListener('loadedmetadata', () => {
                isCameraReady = true;
                console.log('Video ready:', {
                    width: video.videoWidth,
                    height: video.videoHeight
                });
                showMessage("Camera ready! Please position your face in the center and capture.", "info");
            });
        })
        .catch((err) => {
            console.error("Error accessing webcam:", err);
            // Try with basic constraints if advanced ones fail
            navigator.mediaDevices.getUserMedia({ video: true })
                .then((stream) => {
                    video.srcObject = stream;
                    video.addEventListener('loadedmetadata', () => {
                        isCameraReady = true;
                        showMessage("Camera ready with basic settings! Please capture your face.", "info");
                    });
                })
                .catch((basicErr) => {
                    console.error("Basic camera access failed:", basicErr);
                    showMessage("Camera is not accessible. Please allow camera permissions and refresh the page.", "error");
                });
        });

    // Capture face image
    captureBtn.addEventListener("click", function () {
        if (isCapturing) return;

        if (!isCameraReady) {
            showMessage("Camera is not ready yet. Please wait a moment.", "error");
            return;
        }

        if (!video.srcObject) {
            showMessage("Please allow access to your camera first.", "error");
            return;
        }

        if (video.videoWidth === 0 || video.videoHeight === 0) {
            showMessage("Camera is not ready yet. Please wait a moment.", "error");
            return;
        }

        isCapturing = true;
        captureBtn.disabled = true;
        captureBtn.textContent = "Capturing...";

        try {
            const context = canvas.getContext("2d");

            // Set optimal canvas size for face recognition
            const optimalWidth = 640;
            const optimalHeight = 480;
            canvas.width = optimalWidth;
            canvas.height = optimalHeight;

            // Calculate scaling to maintain aspect ratio
            const videoAspect = video.videoWidth / video.videoHeight;
            const canvasAspect = optimalWidth / optimalHeight;

            let drawWidth, drawHeight, offsetX, offsetY;

            if (videoAspect > canvasAspect) {
                // Video is wider than canvas
                drawHeight = optimalHeight;
                drawWidth = drawHeight * videoAspect;
                offsetX = (optimalWidth - drawWidth) / 2;
                offsetY = 0;
            } else {
                // Video is taller than canvas
                drawWidth = optimalWidth;
                drawHeight = drawWidth / videoAspect;
                offsetX = 0;
                offsetY = (optimalHeight - drawHeight) / 2;
            }

            // Fill background with black
            context.fillStyle = '#000000';
            context.fillRect(0, 0, optimalWidth, optimalHeight);

            // Draw video frame with proper scaling
            context.drawImage(video, offsetX, offsetY, drawWidth, drawHeight);

            // Convert captured image to base64 with high quality
            capturedImage = canvas.toDataURL("image/jpeg", 0.95);

            // Hide the canvas (optional)
            canvas.style.display = "none";

            console.log('Image captured:', {
                originalSize: `${video.videoWidth}x${video.videoHeight}`,
                canvasSize: `${canvas.width}x${canvas.height}`,
                imageDataLength: capturedImage.length
            });

            // Show success message
            showToast("Face Captured Successfully!");
            showMessage("Face captured! You can now login.", "success");
            captureBtn.textContent = "Recapture Face";

            console.log("Face captured successfully.");
        } catch (error) {
            console.error("Error capturing image:", error);
            showMessage("Failed to capture image. Please try again.", "error");
            captureBtn.textContent = "Capture Face";
        } finally {
            isCapturing = false;
            captureBtn.disabled = false;
        }
    });

    // Handle form submission
    loginForm.addEventListener("submit", function (e) {
        e.preventDefault();

        const username = document.getElementById("username").value.trim();

        // Validation
        if (!username) {
            showMessage("Please enter your username.", "error");
            return;
        }

        if (!capturedImage) {
            showMessage("Please capture your face before logging in.", "error");
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.textContent = "Logging in...";
        showMessage("Verifying your identity...", "info");

        fetch("/login/", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": getCSRFToken()
            },
            body: JSON.stringify({
                username: username,
                face_image: capturedImage
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === "success") {
                showMessage("Login successful! Welcome back!", "success");
                showToast("Login Successful!");

                // Redirect or perform post-login actions here
                setTimeout(() => {
                    // You can redirect to dashboard or home page here
                    // window.location.href = '/dashboard/';
                    showMessage("You are now logged in!", "success");
                }, 1500);
            } else {
                showMessage(data.message || "Login failed. Please try again.", "error");
            }
        })
        .catch(error => {
            console.error("Login error:", error);
            showMessage("Network error. Please check your connection and try again.", "error");
        })
        .finally(() => {
            submitButton.disabled = false;
            submitButton.textContent = "Login";
        });
    });

    // Function to get CSRF token from cookies
    function getCSRFToken() {
        const cookieValue = document.cookie.match(/csrftoken=([^;]+)/);
        return cookieValue ? cookieValue[1] : "";
    }

    // Helper function to show messages with different styles
    function showMessage(message, type = "info") {
        messageDiv.innerHTML = message;
        messageDiv.className = `message ${type}`;

        // Set appropriate colors
        switch(type) {
            case "success":
                messageDiv.style.color = "green";
                break;
            case "error":
                messageDiv.style.color = "red";
                break;
            case "info":
                messageDiv.style.color = "blue";
                break;
            default:
                messageDiv.style.color = "black";
        }

        // Remove message after 5 seconds for success/info messages
        if (type === "success" || type === "info") {
            setTimeout(() => {
                if (messageDiv.innerHTML === message) {
                    messageDiv.innerHTML = "";
                    messageDiv.className = "";
                    messageDiv.style.color = "";
                }
            }, 5000);
        }
    }

    // Toast Notification Function
    function showToast(message) {
        const toast = document.createElement("div");
        toast.innerText = message;
        toast.style.position = "fixed";
        toast.style.bottom = "20px";
        toast.style.left = "50%";
        toast.style.transform = "translateX(-50%)";
        toast.style.background = "#4CAF50";
        toast.style.color = "white";
        toast.style.padding = "12px 20px";
        toast.style.borderRadius = "5px";
        toast.style.boxShadow = "0px 4px 10px rgba(0, 0, 0, 0.1)";
        toast.style.zIndex = "1000";
        toast.style.fontSize = "16px";
        toast.style.fontWeight = "bold";
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = "0";
            setTimeout(() => toast.remove(), 250);
        }, 2000);
    }
});
