/* === Ultra Premium Professional Login Design === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --error-gradient: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    --glass-bg: rgba(255, 255, 255, 0.12);
    --glass-border: rgba(255, 255, 255, 0.2);
    --text-dark: #2d3748;
    --text-light: #f7fafc;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --transition-base: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-dark);
    overflow-x: hidden;
    perspective: 1000px;
}

/* Animated background particles with 3D effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255,255,255,0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255,255,255,0.08) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.15)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.15)"/><circle cx="90" cy="90" r="1.2" fill="rgba(255,255,255,0.15)"/><circle cx="10" cy="60" r="1.5" fill="rgba(255,255,255,0.15)"/><circle cx="50" cy="30" r="1.8" fill="rgba(255,255,255,0.15)"/><circle cx="30" cy="50" r="1.2" fill="rgba(255,255,255,0.15)"/></svg>') repeat;
    animation: float 25s infinite linear, pulse 15s infinite alternate;
    pointer-events: none;
    z-index: -1;
    opacity: 0.8;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg) scale(1); }
    50% { transform: translateY(-50vh) rotate(180deg) scale(1.05); }
    100% { transform: translateY(-100vh) rotate(360deg) scale(1); }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    100% { opacity: 0.9; }
}

/* === Premium Glass Morphism Container with 3D Tilt Effect === */
.login-container {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    border: 1px solid var(--glass-border);
    border-radius: 28px;
    padding: 48px;
    width: 440px;
    max-width: 90vw;
    text-align: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: var(--transition-base);
    animation: slideUp 0.8s ease-out, fadeIn 0.8s ease-out;
    transform-style: preserve-3d;
    will-change: transform;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.login-container:hover {
    transform: translateY(-5px) rotateX(1deg) rotateY(1deg);
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.2),
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* === Modern Typography with Enhanced Effects === */
h2 {
    font-size: 32px;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

h2::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    margin: 16px auto 28px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    transform-origin: center;
    transition: var(--transition-bounce);
}

.login-container:hover h2::after {
    transform: scaleX(1.1);
}

/* === Premium Camera Section with Enhanced Effects === */
.camera-container {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    margin-bottom: 32px;
}

/* === Face Detection Guide === */
.face-guide {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 180px;
    height: 220px;
    border: 3px solid rgba(102, 126, 234, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 10;
    animation: pulse 2s infinite;
}

.face-guide::before {
    content: 'Position your face here';
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(102, 126, 234, 0.8);
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

@keyframes pulse {
    0%, 100% {
        border-color: rgba(102, 126, 234, 0.6);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
    }
    50% {
        border-color: rgba(102, 126, 234, 0.9);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0.1);
    }
}

.face-guide.hidden {
    display: none;
}

/* === Futuristic Video Stream with Holographic Effect === */
video {
    width: 100%;
    max-width: 320px;
    height: 220px;
    object-fit: cover;
    border-radius: 24px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 20px rgba(102, 126, 234, 0.3);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
}

video::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.15) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
    z-index: 2;
}

video::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 10%;
    width: 80%;
    height: 20px;
    background: rgba(255, 255, 255, 0.05);
    filter: blur(10px);
    border-radius: 50%;
    z-index: 1;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(100%) skewX(-15deg); }
}

video:hover {
    transform: scale(1.03) translateZ(10px);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(102, 126, 234, 0.5);
}

/* === Premium Capture Button with 3D Press Effect === */
button#capture-btn {
    position: relative;
    padding: 16px 32px;
    border: none;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.4),
        0 3px 10px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transform-style: preserve-3d;
    will-change: transform;
}

button#capture-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.7s ease-in-out;
}

button#capture-btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.2) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

button#capture-btn:hover::before {
    left: 100%;
}

button#capture-btn:hover::after {
    opacity: 1;
}

button#capture-btn:hover {
    transform: translateY(-4px) translateZ(5px);
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.6),
        0 6px 15px rgba(0, 0, 0, 0.2);
}

button#capture-btn:active {
    transform: translateY(2px) translateZ(0);
    box-shadow:
        0 3px 10px rgba(102, 126, 234, 0.4),
        0 1px 5px rgba(0, 0, 0, 0.1);
}

/* === Modern Form Design === */
form {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    margin-top: 24px;
    width: 100%;
}

/* === Premium Input Fields with Floating Labels === */
.input-group {
    position: relative;
    width: 100%;
    perspective: 1000px;
}

.input-group input {
    width: 100%;
    padding: 18px 24px;
    border: 2px solid var(--glass-border);
    border-radius: 18px;
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    font-size: 16px;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition-base);
    outline: none;
    box-shadow: var(--shadow-sm);
    transform-style: preserve-3d;
}

.input-group input::placeholder {
    color: rgba(45, 55, 72, 0.6);
    font-weight: 400;
    transition: all 0.2s ease;
}

.input-group input:focus::placeholder {
    opacity: 0.3;
    transform: translateX(5px);
}

.input-group input:focus {
    border-color: rgba(102, 126, 234, 0.8);
    background: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.15),
        var(--shadow-md);
    transform: translateY(-2px) translateZ(5px);
}

.input-group input:valid {
    border-color: rgba(72, 187, 120, 0.7);
}

/* === Premium Submit Button with Depth Effect === */
button[type="submit"] {
    position: relative;
    width: 100%;
    padding: 18px 24px;
    border: none;
    background: var(--success-gradient);
    color: white;
    border-radius: 18px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    box-shadow:
        0 6px 20px rgba(72, 187, 120, 0.4),
        0 3px 10px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    transform-style: preserve-3d;
    will-change: transform;
}

button[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.7s ease-in-out;
}

button[type="submit"]::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.2) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

button[type="submit"]:hover::before {
    left: 100%;
}

button[type="submit"]:hover::after {
    opacity: 1;
}

button[type="submit"]:hover {
    transform: translateY(-4px) translateZ(5px);
    box-shadow:
        0 10px 30px rgba(72, 187, 120, 0.6),
        0 6px 15px rgba(0, 0, 0, 0.2);
}

button[type="submit"]:active {
    transform: translateY(2px) translateZ(0);
    box-shadow:
        0 3px 10px rgba(72, 187, 120, 0.4),
        0 1px 5px rgba(0, 0, 0, 0.1);
}

/* === Premium Message Display with Entrance Animation === */
#message {
    margin-top: 24px;
    font-size: 15px;
    font-weight: 500;
    padding: 18px 24px;
    border-radius: 16px;
    transition: var(--transition-base);
    min-height: 20px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    animation: messageSlide 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-origin: top center;
    box-shadow: var(--shadow-sm);
    line-height: 1.5;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

#message.message.success {
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.2), rgba(56, 161, 105, 0.15));
    border-color: rgba(72, 187, 120, 0.4);
    color: #38a169;
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.25);
}

#message.message.error {
    background: linear-gradient(135deg, rgba(245, 101, 101, 0.2), rgba(229, 62, 62, 0.15));
    border-color: rgba(245, 101, 101, 0.4);
    color: #e53e3e;
    box-shadow: 0 6px 20px rgba(245, 101, 101, 0.25);
}

#message.message.info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.15));
    border-color: rgba(102, 126, 234, 0.4);
    color: #667eea;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
}

/* === Premium Button States === */
button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    background: rgba(160, 174, 192, 0.6) !important;
}

button:disabled::before,
button:disabled::after {
    display: none;
}

/* === Loading Animation with 3D Effect === */
.loading {
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.8s infinite;
    transform: translateZ(10px);
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* === Advanced Responsive Design === */
@media (max-width: 768px) {
    .login-container {
        width: 95%;
        padding: 36px 24px;
        margin: 20px;
        border-radius: 24px;
    }

    h2 {
        font-size: 28px;
    }

    h2::after {
        width: 70px;
        margin: 12px auto 24px;
    }

    video {
        max-width: 300px;
        height: 200px;
    }
}

@media (max-width: 480px) {
    .login-container {
        width: 100%;
        margin: 10px;
        padding: 32px 20px;
        border-radius: 20px;
    }

    h2 {
        font-size: 26px;
    }

    video {
        max-width: 280px;
        height: 180px;
    }

    button#capture-btn,
    button[type="submit"] {
        padding: 16px 24px;
        font-size: 15px;
    }

    .input-group input {
        padding: 16px 20px;
        font-size: 15px;
    }
}

@media (max-width: 375px) {
    .login-container {
        padding: 28px 16px;
    }

    video {
        max-width: 260px;
        height: 160px;
    }
}

/* === Dark mode support with Smooth Transition === */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        color: var(--text-light);
        transition: background 0.5s ease;
    }

    .login-container {
        background: rgba(26, 32, 44, 0.85);
        border-color: rgba(255, 255, 255, 0.15);
        transition: var(--transition-base);
    }

    .input-group input {
        color: var(--text-light);
        background: rgba(26, 32, 44, 0.7);
    }

    .input-group input::placeholder {
        color: rgba(226, 232, 240, 0.6);
    }

    .input-group input:focus {
        background: rgba(26, 32, 44, 0.8);
    }
}

/* === Micro-interactions for Enhanced UX === */
.input-group {
    --focus-scale: 1.02;
    --focus-translate: -2px;
}

.input-group:focus-within {
    transform: scale(var(--focus-scale)) translateY(var(--focus-translate));
    transition: var(--transition-bounce);
}

/* === Accessibility Improvements === */
input:focus-visible,
button:focus-visible {
    outline: 2px solid rgba(102, 126, 234, 0.8);
    outline-offset: 2px;
}

/* === Performance Optimizations === */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}